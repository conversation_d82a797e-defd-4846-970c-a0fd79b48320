# Voice AI Agent - Setup Instructions for AI Coder

## Project Overview
This guide provides step-by-step instructions for building a global voice assistant application using:
- **Deepgram** (Speech-to-Text)
- **Gemini** (Language Model)
- **ElevenLabs** (Text-to-Speech)
- **Python** (Primary language with official SDKs)

---

## Step 1: Project Setup and Technology Selection

### Objective
Set up the foundational structure for the voice assistant application using Python and create the initial project environment.

### Instructions for AI Coder

#### 1. Create Project Directory
- **Action:** Create a new folder named `global-voice-assistant`
- **Location:** This will be the root directory for all project files
- **Path:** Choose an appropriate location on your system

#### 2. Set Up Virtual Environment
- **Directory:** Navigate to `global-voice-assistant` directory
- **Command (WSL2/Ubuntu):**
  ```bash
  python -m venv .venv
  ```
- **Activate Virtual Environment:**
  ```bash
  source .venv/bin/activate
  ```
- **Note:** On PowerShell, use `.venv\Scripts\Activate.ps1`

#### 3. Create Core Project Files
Create the following empty files inside the `global-voice-assistant` directory:

- **`main.py`** - Main entry point for application logic
- **`requirements.txt`** - Python dependencies list
- **`.env`** - Secure storage for API keys and secrets (never commit to version control)

#### 4. Populate `requirements.txt`
Add the following dependencies to `requirements.txt`:

```
python-dotenv
deepgram-sdk
google-generativeai
elevenlabs
sounddevice
scipy
```

**Dependency Purposes:**
- `python-dotenv`: Load environment variables from .env file
- `deepgram-sdk`: Deepgram Speech-to-Text SDK
- `google-generativeai`: Gemini AI SDK
- `elevenlabs`: ElevenLabs Text-to-Speech SDK
- `sounddevice`: Audio playback functionality
- `scipy`: Audio processing support

#### 5. Install Dependencies
- **Directory:** `global-voice-assistant` (with virtual environment activated)
- **Command:**
  ```bash
  pip install -r requirements.txt
  ```
- **Note:** Ensure virtual environment is activated before running this command

#### 6. Configure Environment Variables
Open the `.env` file and add the following template:

```
DEEPGRAM_API_KEY="YOUR_DEEPGRAM_API_KEY"
GEMINI_API_KEY="YOUR_GEMINI_API_KEY"
ELEVENLABS_API_KEY="YOUR_ELEVENLABS_API_KEY"
```

**Important:**
- Replace placeholder text with actual API keys
- Never commit `.env` file to version control
- Add `.env` to `.gitignore` if using Git

---

## Verification Checkpoint

**Before proceeding to Step 2:**
1. Provide a directory tree map of the `global-voice-assistant` directory
2. Confirm all dependencies installed successfully
3. Verify `.env` file contains all required API keys

**Expected Directory Structure:**
```
global-voice-assistant/
├── .venv/
├── .env
├── main.py
└── requirements.txt
```

---

## Security Notes
- All API keys must be stored in `.env` file only
- Sanitize and validate all user inputs
- Encode outputs to prevent XSS attacks
- Never expose API keys in code or logs

---

## Next Steps
Once Step 1 is verified and complete, proceed to Step 2: Testing Individual Service Connections
