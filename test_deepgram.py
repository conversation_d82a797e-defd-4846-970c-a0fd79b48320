import os
from dotenv import load_dotenv
from deepgram import (
    DeepgramClient,
    DeepgramClientOptions,
    LiveTranscriptionEvents,
    LiveOptions,
)

load_dotenv()

# URL for the realtime streaming audio endpoint
# The 'nova-2' model is a good default for conversation
DEEPGRAM_API_KEY = os.getenv("DEEPGRAM_API_KEY")

def main():
    try:
        # STEP 1: Create a Deepgram client using the API key
        config = DeepgramClientOptions(
            verbose=0,  # Set to 1 for detailed logging
        )
        deepgram = DeepgramClient(DEEPGRAM_API_KEY, config)

        # STEP 2: Create a websocket connection to Deepgram
        dg_connection = deepgram.listen.asynclive.v("1")

        # STEP 3: Define a function to handle transcription results
        async def on_message(self, result, **kwargs):
            sentence = result.channel.alternatives[0].transcript
            if len(sentence) == 0:
                return
            print(f"transcript: {sentence}")

        # STEP 4: Register the event handler
        dg_connection.on(LiveTranscriptionEvents.Transcript, on_message)

        # STEP 5: Define options for the live transcription
        options = LiveOptions(
            model="nova-2",
            language="en-US",
            smart_format=True,
        )

        # STEP 6: Start the connection
        print("\n\nPress Ctrl+C to stop...\n")
        await dg_connection.start(options)

    except Exception as e:
        print(f"Could not open socket: {e}")
        return

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())