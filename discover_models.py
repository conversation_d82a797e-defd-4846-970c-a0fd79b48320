import os
import google.generativeai as genai
from dotenv import load_dotenv

try:
    load_dotenv()
    api_key = os.environ.get("GEMINI_API_KEY")
    if not api_key:
        raise ValueError("GEMINI_API_KEY not found.")

    genai.configure(api_key=api_key)

    print("Fetching available models for your API key...")
    print("------------------------------------------")

    for model in genai.list_models():
        if 'generateContent' in model.supported_generation_methods:
            print(f"- {model.name}")

    print("------------------------------------------")
    print("Please use one of the model names listed above in the test_gemini.py script.")

except Exception as e:
    print(f"An error occurred: {e}")