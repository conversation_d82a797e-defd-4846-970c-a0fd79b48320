import os
import google.generativeai as genai
from dotenv import load_dotenv

try:
    # Load environment variables from .env file
    load_dotenv()

    # Configure the generative AI client
    api_key = os.environ.get("GEMINI_API_KEY")
    if not api_key:
        raise ValueError("GEMINI_API_KEY not found. Make sure it's set in your system environment variables.")

    genai.configure(api_key=api_key)

    # Create the model
    # model = genai.GenerativeModel('gemini-pro')
    # model = genai.GenerativeModel('gemini-1.5-flash-latest')
    model = genai.GenerativeModel('models/gemini-pro-latest')

    # Send a prompt
    print("Sending prompt to Gemini...")
    response = model.generate_content("Tell me a fun fact about the Roman Empire.")

    # Print the response
    print("\n--- Gemini Response ---")
    print(response.text)
    print("-----------------------\n")

except Exception as e:
    print(f"An error occurred: {e}")